"""
Emotion recognition utilities for real-time processing
"""

import cv2
import numpy as np
import time
from collections import deque
import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.emotion_model import EmotionCNN
from utils.face_detection import FaceDetector
from utils.data_preprocessing import DataPreprocessor

class RealTimeEmotionRecognizer:
    def __init__(self, model_path="models/saved_models/emotion_model.h5"):
        """
        Initialize the real-time emotion recognizer
        
        Args:
            model_path: Path to the trained emotion recognition model
        """
        self.model_path = model_path
        self.emotion_cnn = EmotionCNN()
        self.face_detector = FaceDetector()
        self.preprocessor = DataPreprocessor()
        
        # Emotion smoothing
        self.emotion_history = deque(maxlen=10)  # Store last 10 predictions
        self.confidence_threshold = 0.3
        
        # Performance tracking
        self.fps_counter = deque(maxlen=30)
        self.last_time = time.time()
        
        # Load model
        self.load_model()
    
    def load_model(self):
        """
        Load the trained emotion recognition model
        """
        if os.path.exists(self.model_path):
            success = self.emotion_cnn.load_model(self.model_path)
            if success:
                print("Emotion recognition model loaded successfully!")
                return True
            else:
                print("Failed to load emotion recognition model.")
                return False
        else:
            print(f"Model file not found: {self.model_path}")
            print("Please train the model first using models/train_model.py")
            return False
    
    def smooth_emotion_prediction(self, emotion, confidence):
        """
        Smooth emotion predictions over time to reduce flickering
        
        Args:
            emotion: Current predicted emotion
            confidence: Confidence score
            
        Returns:
            Smoothed emotion and confidence
        """
        # Add current prediction to history
        self.emotion_history.append((emotion, confidence))
        
        if len(self.emotion_history) < 3:
            return emotion, confidence
        
        # Count occurrences of each emotion in recent history
        emotion_counts = {}
        total_confidence = 0
        
        for hist_emotion, hist_confidence in self.emotion_history:
            if hist_confidence > self.confidence_threshold:
                if hist_emotion not in emotion_counts:
                    emotion_counts[hist_emotion] = []
                emotion_counts[hist_emotion].append(hist_confidence)
                total_confidence += hist_confidence
        
        if not emotion_counts:
            return emotion, confidence
        
        # Find most frequent emotion with highest average confidence
        best_emotion = emotion
        best_score = 0
        
        for emo, confidences in emotion_counts.items():
            avg_confidence = np.mean(confidences)
            frequency_weight = len(confidences) / len(self.emotion_history)
            score = avg_confidence * frequency_weight
            
            if score > best_score:
                best_score = score
                best_emotion = emo
        
        return best_emotion, best_score
    
    def calculate_fps(self):
        """
        Calculate current FPS
        """
        current_time = time.time()
        fps = 1.0 / (current_time - self.last_time)
        self.fps_counter.append(fps)
        self.last_time = current_time
        
        return np.mean(self.fps_counter) if self.fps_counter else 0
    
    def process_frame(self, frame):
        """
        Process a single frame for emotion recognition
        
        Args:
            frame: Input video frame
            
        Returns:
            Processed frame with emotion annotations
        """
        if self.emotion_cnn.model is None:
            # Draw error message if model not loaded
            cv2.putText(frame, "Model not loaded!", (50, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            return frame
        
        # Detect faces
        face_coords, face_images = self.face_detector.detect_faces(frame)
        
        emotions = []
        confidences = []
        
        # Process each detected face
        for face_img in face_images:
            if face_img.size > 0:
                # Preprocess face for emotion recognition
                processed_face = self.preprocessor.preprocess_face_for_emotion(face_img)
                
                # Predict emotion
                emotion, confidence, all_predictions = self.emotion_cnn.predict_emotion(processed_face)
                
                # Apply smoothing
                smoothed_emotion, smoothed_confidence = self.smooth_emotion_prediction(emotion, confidence)
                
                emotions.append(smoothed_emotion)
                confidences.append(smoothed_confidence)
        
        # Draw face boxes and emotion labels
        result_frame = self.face_detector.draw_face_boxes(frame, face_coords, emotions, confidences)
        
        # Add FPS counter
        fps = self.calculate_fps()
        cv2.putText(result_frame, f"FPS: {fps:.1f}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # Add instructions
        cv2.putText(result_frame, "Press 'q' to quit", (10, result_frame.shape[0] - 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return result_frame
    
    def start_webcam_recognition(self, camera_index=0):
        """
        Start real-time emotion recognition from webcam
        
        Args:
            camera_index: Camera index (0 for default camera)
        """
        print("Starting real-time emotion recognition...")
        print("Press 'q' to quit")
        
        # Initialize camera
        cap = cv2.VideoCapture(camera_index)
        
        if not cap.isOpened():
            print("Error: Could not open camera")
            return
        
        # Set camera properties for better performance
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        try:
            while True:
                # Read frame from camera
                ret, frame = cap.read()
                
                if not ret:
                    print("Error: Could not read frame from camera")
                    break
                
                # Process frame
                processed_frame = self.process_frame(frame)
                
                # Display result
                cv2.imshow('Real-time Emotion Recognition', processed_frame)
                
                # Check for quit key
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('r'):
                    # Reset emotion history
                    self.emotion_history.clear()
                    print("Emotion history reset")
        
        except KeyboardInterrupt:
            print("\nInterrupted by user")
        
        finally:
            # Clean up
            cap.release()
            cv2.destroyAllWindows()
            print("Camera released and windows closed")
    
    def process_video_file(self, video_path, output_path=None):
        """
        Process a video file for emotion recognition
        
        Args:
            video_path: Path to input video file
            output_path: Path to save output video (optional)
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            print(f"Error: Could not open video file {video_path}")
            return
        
        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"Processing video: {video_path}")
        print(f"Resolution: {width}x{height}, FPS: {fps}, Total frames: {total_frames}")
        
        # Setup video writer if output path is provided
        writer = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        frame_count = 0
        
        try:
            while True:
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                # Process frame
                processed_frame = self.process_frame(frame)
                
                # Write frame if output is specified
                if writer:
                    writer.write(processed_frame)
                
                # Display progress
                frame_count += 1
                if frame_count % 30 == 0:
                    progress = (frame_count / total_frames) * 100
                    print(f"Progress: {progress:.1f}%")
                
                # Display frame (optional, comment out for faster processing)
                cv2.imshow('Video Processing', processed_frame)
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
        
        finally:
            cap.release()
            if writer:
                writer.release()
            cv2.destroyAllWindows()
            
            if output_path:
                print(f"Processed video saved to: {output_path}")

if __name__ == "__main__":
    # Test the emotion recognizer
    recognizer = RealTimeEmotionRecognizer()
    
    if recognizer.emotion_cnn.model is not None:
        recognizer.start_webcam_recognition()
    else:
        print("Please train the model first using: python models/train_model.py")
