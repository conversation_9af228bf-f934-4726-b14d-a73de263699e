"""
CNN Model Architecture for Facial Emotion Recognition
"""

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import numpy as np

class EmotionCNN:
    def __init__(self, input_shape=(48, 48, 1), num_classes=7):
        """
        Initialize the Emotion CNN model
        
        Args:
            input_shape: Shape of input images (height, width, channels)
            num_classes: Number of emotion classes (default: 7)
        """
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.model = None
        self.emotion_labels = ['Angry', 'Disgust', 'Fear', 'Happy', 'Neutral', 'Sad', 'Surprise']
    
    def build_model(self):
        """
        Build the CNN architecture for emotion recognition
        """
        model = keras.Sequential([
            # First Convolutional Block
            layers.Conv2D(32, (3, 3), activation='relu', input_shape=self.input_shape),
            layers.BatchNormalization(),
            layers.Conv2D(32, (3, 3), activation='relu'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Second Convolutional Block
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Third Convolutional Block
            layers.Conv2D(128, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.Conv2D(128, (3, 3), activation='relu'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Fourth Convolutional Block
            layers.Conv2D(256, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.25),
            
            # Flatten and Dense Layers
            layers.Flatten(),
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(256, activation='relu'),
            layers.Dropout(0.5),
            layers.Dense(self.num_classes, activation='softmax')
        ])
        
        self.model = model
        return model
    
    def compile_model(self, learning_rate=0.001):
        """
        Compile the model with optimizer, loss, and metrics
        """
        if self.model is None:
            self.build_model()
        
        optimizer = keras.optimizers.Adam(learning_rate=learning_rate)
        
        self.model.compile(
            optimizer=optimizer,
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
    
    def get_model_summary(self):
        """
        Get model architecture summary
        """
        if self.model is None:
            self.build_model()
        return self.model.summary()
    
    def save_model(self, filepath):
        """
        Save the trained model
        """
        if self.model is not None:
            self.model.save(filepath)
            print(f"Model saved to {filepath}")
        else:
            print("No model to save. Please build and train the model first.")
    
    def load_model(self, filepath):
        """
        Load a pre-trained model
        """
        try:
            self.model = keras.models.load_model(filepath)
            print(f"Model loaded from {filepath}")
            return True
        except Exception as e:
            print(f"Error loading model: {e}")
            return False
    
    def predict_emotion(self, image):
        """
        Predict emotion from a preprocessed image
        
        Args:
            image: Preprocessed image array
            
        Returns:
            Predicted emotion label and confidence scores
        """
        if self.model is None:
            raise ValueError("Model not loaded. Please load or train a model first.")
        
        # Ensure image has correct shape
        if len(image.shape) == 2:
            image = np.expand_dims(image, axis=-1)
        if len(image.shape) == 3:
            image = np.expand_dims(image, axis=0)
        
        # Make prediction
        predictions = self.model.predict(image, verbose=0)
        predicted_class = np.argmax(predictions[0])
        confidence = predictions[0][predicted_class]
        
        return self.emotion_labels[predicted_class], confidence, predictions[0]

def create_emotion_model():
    """
    Factory function to create and return an EmotionCNN instance
    """
    emotion_cnn = EmotionCNN()
    emotion_cnn.build_model()
    emotion_cnn.compile_model()
    return emotion_cnn

if __name__ == "__main__":
    # Test model creation
    model = create_emotion_model()
    print("Model created successfully!")
    print(model.get_model_summary())
