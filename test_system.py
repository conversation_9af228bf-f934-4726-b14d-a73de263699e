"""
Test script for the Real-time Facial Emotion Recognition System
"""

import unittest
import numpy as np
import cv2
import os
import sys
import tempfile

# Add current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.emotion_model import EmotionCNN, create_emotion_model
from utils.data_preprocessing import DataPreprocessor, create_sample_data
from utils.face_detection import FaceDetector
from utils.emotion_utils import RealTimeEmotionRecognizer

class TestEmotionModel(unittest.TestCase):
    """Test cases for the emotion recognition model"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.emotion_cnn = EmotionCNN()
    
    def test_model_creation(self):
        """Test model creation and compilation"""
        model = self.emotion_cnn.build_model()
        self.assertIsNotNone(model)
        
        # Test compilation
        self.emotion_cnn.compile_model()
        self.assertIsNotNone(self.emotion_cnn.model)
    
    def test_model_prediction_shape(self):
        """Test model prediction output shape"""
        self.emotion_cnn.build_model()
        self.emotion_cnn.compile_model()
        
        # Create sample input
        sample_input = np.random.rand(1, 48, 48, 1).astype('float32')
        
        # Make prediction
        prediction = self.emotion_cnn.model.predict(sample_input, verbose=0)
        
        # Check output shape
        self.assertEqual(prediction.shape, (1, 7))
        
        # Check if probabilities sum to 1
        self.assertAlmostEqual(np.sum(prediction[0]), 1.0, places=5)
    
    def test_emotion_labels(self):
        """Test emotion labels"""
        expected_labels = ['Angry', 'Disgust', 'Fear', 'Happy', 'Neutral', 'Sad', 'Surprise']
        self.assertEqual(self.emotion_cnn.emotion_labels, expected_labels)
    
    def test_factory_function(self):
        """Test the factory function for creating models"""
        model = create_emotion_model()
        self.assertIsNotNone(model)
        self.assertIsNotNone(model.model)

class TestDataPreprocessing(unittest.TestCase):
    """Test cases for data preprocessing"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.preprocessor = DataPreprocessor()
    
    def test_image_preprocessing(self):
        """Test image preprocessing"""
        # Create sample image
        sample_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        # Preprocess
        processed = self.preprocessor.preprocess_image(sample_image)
        
        # Check output shape
        self.assertEqual(processed.shape, (48, 48, 1))
        
        # Check value range
        self.assertTrue(np.all(processed >= 0))
        self.assertTrue(np.all(processed <= 1))
    
    def test_face_preprocessing(self):
        """Test face-specific preprocessing"""
        # Create sample face image
        face_image = np.random.randint(0, 255, (80, 80, 3), dtype=np.uint8)
        
        # Preprocess
        processed = self.preprocessor.preprocess_face_for_emotion(face_image)
        
        # Check output shape
        self.assertEqual(processed.shape, (48, 48, 1))
        
        # Check value range
        self.assertTrue(np.all(processed >= 0))
        self.assertTrue(np.all(processed <= 1))
    
    def test_sample_data_creation(self):
        """Test sample data creation"""
        X, y = create_sample_data()
        
        # Check shapes
        self.assertEqual(X.shape, (1000, 48, 48, 1))
        self.assertEqual(y.shape, (1000, 7))
        
        # Check data types
        self.assertEqual(X.dtype, np.float32)
        
        # Check value ranges
        self.assertTrue(np.all(X >= 0))
        self.assertTrue(np.all(X <= 1))

class TestFaceDetection(unittest.TestCase):
    """Test cases for face detection"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.face_detector = FaceDetector()
    
    def test_detector_initialization(self):
        """Test face detector initialization"""
        self.assertIsNotNone(self.face_detector.face_cascade)
    
    def test_face_detection_with_sample_image(self):
        """Test face detection with sample image"""
        # Create sample image
        sample_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Detect faces
        faces, face_images = self.face_detector.detect_faces(sample_image)
        
        # Check return types
        self.assertIsInstance(faces, np.ndarray)
        self.assertIsInstance(face_images, list)
    
    def test_face_centering_check(self):
        """Test face centering check"""
        # Test with centered face
        centered_face = (200, 150, 100, 100)  # x, y, w, h
        image_shape = (400, 500)  # height, width
        
        is_centered = self.face_detector.is_face_centered(centered_face, image_shape)
        self.assertTrue(is_centered)
        
        # Test with off-center face
        off_center_face = (50, 50, 100, 100)
        is_centered = self.face_detector.is_face_centered(off_center_face, image_shape)
        self.assertFalse(is_centered)

class TestSystemIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create a temporary model for testing
        self.temp_model_path = tempfile.mktemp(suffix='.h5')
        
        # Create and save a simple model
        emotion_cnn = EmotionCNN()
        emotion_cnn.build_model()
        emotion_cnn.compile_model()
        emotion_cnn.save_model(self.temp_model_path)
    
    def tearDown(self):
        """Clean up test fixtures"""
        if os.path.exists(self.temp_model_path):
            os.remove(self.temp_model_path)
    
    def test_emotion_recognizer_initialization(self):
        """Test emotion recognizer initialization"""
        recognizer = RealTimeEmotionRecognizer(self.temp_model_path)
        self.assertIsNotNone(recognizer.emotion_cnn)
        self.assertIsNotNone(recognizer.face_detector)
        self.assertIsNotNone(recognizer.preprocessor)
    
    def test_frame_processing(self):
        """Test frame processing pipeline"""
        recognizer = RealTimeEmotionRecognizer(self.temp_model_path)
        
        # Create sample frame
        frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Process frame
        processed_frame = recognizer.process_frame(frame)
        
        # Check output
        self.assertIsNotNone(processed_frame)
        self.assertEqual(processed_frame.shape, frame.shape)

class TestModelPerformance(unittest.TestCase):
    """Performance tests for the model"""
    
    def test_prediction_speed(self):
        """Test prediction speed"""
        import time
        
        # Create model
        emotion_cnn = EmotionCNN()
        emotion_cnn.build_model()
        emotion_cnn.compile_model()
        
        # Create sample data
        sample_data = np.random.rand(10, 48, 48, 1).astype('float32')
        
        # Measure prediction time
        start_time = time.time()
        predictions = emotion_cnn.model.predict(sample_data, verbose=0)
        end_time = time.time()
        
        prediction_time = end_time - start_time
        
        # Check that predictions are fast enough (less than 1 second for 10 images)
        self.assertLess(prediction_time, 1.0)
        
        # Check prediction shape
        self.assertEqual(predictions.shape, (10, 7))

def run_system_tests():
    """Run all system tests"""
    print("Running Real-time Facial Emotion Recognition System Tests")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestEmotionModel))
    test_suite.addTest(unittest.makeSuite(TestDataPreprocessing))
    test_suite.addTest(unittest.makeSuite(TestFaceDetection))
    test_suite.addTest(unittest.makeSuite(TestSystemIntegration))
    test_suite.addTest(unittest.makeSuite(TestModelPerformance))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\nOverall Result: {'PASS' if success else 'FAIL'}")
    
    return success

def check_system_requirements():
    """Check if system meets requirements for emotion recognition"""
    print("Checking System Requirements...")
    print("-" * 40)
    
    requirements_met = True
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major >= 3 and python_version.minor >= 8:
        print("✓ Python version: OK")
    else:
        print("✗ Python version: Requires Python 3.8+")
        requirements_met = False
    
    # Check required packages
    required_packages = [
        ('tensorflow', 'TensorFlow'),
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('PIL', 'Pillow'),
        ('matplotlib', 'Matplotlib'),
        ('sklearn', 'Scikit-learn')
    ]
    
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"✓ {name}: OK")
        except ImportError:
            print(f"✗ {name}: Not installed")
            requirements_met = False
    
    # Check camera availability
    try:
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            print("✓ Camera: Available")
            cap.release()
        else:
            print("⚠ Camera: Not available (optional for video processing)")
    except:
        print("⚠ Camera: Cannot check availability")
    
    print("-" * 40)
    print(f"System Requirements: {'MET' if requirements_met else 'NOT MET'}")
    
    return requirements_met

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test the Emotion Recognition System")
    parser.add_argument('--check-requirements', action='store_true', 
                       help='Check system requirements')
    parser.add_argument('--run-tests', action='store_true', 
                       help='Run all unit tests')
    
    args = parser.parse_args()
    
    if args.check_requirements:
        check_system_requirements()
    elif args.run_tests:
        run_system_tests()
    else:
        # Run both by default
        print("Checking system requirements first...\n")
        requirements_ok = check_system_requirements()
        
        if requirements_ok:
            print("\nRunning system tests...\n")
            run_system_tests()
        else:
            print("\nPlease install missing requirements before running tests.")
            print("Use: pip install -r requirements.txt")
