"""
GUI Interface for Real-time Facial Emotion Recognition
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
from PIL import Image, ImageTk
import threading
import numpy as np
import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.emotion_utils import RealTimeEmotionRecognizer

class EmotionRecognitionGUI:
    def __init__(self, root):
        """
        Initialize the GUI application
        
        Args:
            root: Tkinter root window
        """
        self.root = root
        self.root.title("Real-time Facial Emotion Recognition")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2c3e50')
        
        # Initialize emotion recognizer
        self.recognizer = RealTimeEmotionRecognizer()
        
        # Video capture variables
        self.cap = None
        self.is_running = False
        self.current_frame = None
        
        # GUI variables
        self.video_label = None
        self.emotion_vars = {}
        self.fps_var = tk.StringVar(value="FPS: 0")
        self.status_var = tk.StringVar(value="Ready")
        
        # Create GUI elements
        self.create_widgets()
        
        # Start GUI update loop
        self.update_gui()
    
    def create_widgets(self):
        """
        Create and arrange GUI widgets
        """
        # Main title
        title_label = tk.Label(
            self.root, 
            text="Real-time Facial Emotion Recognition", 
            font=("Arial", 20, "bold"),
            bg='#2c3e50',
            fg='white'
        )
        title_label.pack(pady=10)
        
        # Main frame
        main_frame = tk.Frame(self.root, bg='#2c3e50')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Left frame for video
        left_frame = tk.Frame(main_frame, bg='#34495e', relief=tk.RAISED, bd=2)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Video display
        video_frame = tk.Frame(left_frame, bg='#34495e')
        video_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.video_label = tk.Label(
            video_frame, 
            text="Camera Feed Will Appear Here",
            bg='black',
            fg='white',
            font=("Arial", 14)
        )
        self.video_label.pack(fill=tk.BOTH, expand=True)
        
        # Control buttons frame
        control_frame = tk.Frame(left_frame, bg='#34495e')
        control_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # Start/Stop button
        self.start_button = tk.Button(
            control_frame,
            text="Start Camera",
            command=self.toggle_camera,
            bg='#27ae60',
            fg='white',
            font=("Arial", 12, "bold"),
            relief=tk.FLAT,
            padx=20
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Process video button
        video_button = tk.Button(
            control_frame,
            text="Process Video File",
            command=self.process_video_file,
            bg='#3498db',
            fg='white',
            font=("Arial", 12, "bold"),
            relief=tk.FLAT,
            padx=20
        )
        video_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Train model button
        train_button = tk.Button(
            control_frame,
            text="Train Model",
            command=self.train_model,
            bg='#e74c3c',
            fg='white',
            font=("Arial", 12, "bold"),
            relief=tk.FLAT,
            padx=20
        )
        train_button.pack(side=tk.LEFT)
        
        # Right frame for information
        right_frame = tk.Frame(main_frame, bg='#34495e', relief=tk.RAISED, bd=2)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_frame.config(width=300)
        
        # Emotion display section
        emotion_frame = tk.LabelFrame(
            right_frame, 
            text="Detected Emotions", 
            bg='#34495e',
            fg='white',
            font=("Arial", 14, "bold")
        )
        emotion_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Create emotion progress bars
        emotions = ['Angry', 'Disgust', 'Fear', 'Happy', 'Neutral', 'Sad', 'Surprise']
        colors = ['#e74c3c', '#8e44ad', '#f39c12', '#f1c40f', '#95a5a6', '#3498db', '#e67e22']
        
        for emotion, color in zip(emotions, colors):
            frame = tk.Frame(emotion_frame, bg='#34495e')
            frame.pack(fill=tk.X, padx=5, pady=2)
            
            label = tk.Label(
                frame, 
                text=emotion, 
                bg='#34495e', 
                fg='white',
                font=("Arial", 10),
                width=8,
                anchor='w'
            )
            label.pack(side=tk.LEFT)
            
            progress = ttk.Progressbar(
                frame, 
                length=150, 
                mode='determinate',
                style=f'{emotion}.Horizontal.TProgressbar'
            )
            progress.pack(side=tk.LEFT, padx=(5, 0))
            
            confidence_label = tk.Label(
                frame,
                text="0%",
                bg='#34495e',
                fg='white',
                font=("Arial", 9),
                width=4
            )
            confidence_label.pack(side=tk.RIGHT)
            
            self.emotion_vars[emotion] = {
                'progress': progress,
                'label': confidence_label
            }
        
        # Statistics section
        stats_frame = tk.LabelFrame(
            right_frame, 
            text="Statistics", 
            bg='#34495e',
            fg='white',
            font=("Arial", 14, "bold")
        )
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # FPS display
        fps_label = tk.Label(
            stats_frame,
            textvariable=self.fps_var,
            bg='#34495e',
            fg='white',
            font=("Arial", 12)
        )
        fps_label.pack(pady=5)
        
        # Model status
        model_status = "Model Loaded" if self.recognizer.emotion_cnn.model else "Model Not Loaded"
        model_color = "#27ae60" if self.recognizer.emotion_cnn.model else "#e74c3c"
        
        model_label = tk.Label(
            stats_frame,
            text=f"Status: {model_status}",
            bg='#34495e',
            fg=model_color,
            font=("Arial", 10)
        )
        model_label.pack(pady=2)
        
        # Instructions section
        instructions_frame = tk.LabelFrame(
            right_frame, 
            text="Instructions", 
            bg='#34495e',
            fg='white',
            font=("Arial", 14, "bold")
        )
        instructions_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        instructions_text = """
1. Click 'Start Camera' to begin real-time emotion detection

2. Position your face in front of the camera

3. The system will detect and classify your emotions

4. Use 'Process Video File' to analyze video files

5. If model is not loaded, click 'Train Model' first

Tips:
• Ensure good lighting
• Keep face centered
• Avoid rapid movements
        """
        
        instructions_label = tk.Label(
            instructions_frame,
            text=instructions_text,
            bg='#34495e',
            fg='white',
            font=("Arial", 9),
            justify=tk.LEFT,
            anchor='nw'
        )
        instructions_label.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Status bar
        status_frame = tk.Frame(self.root, bg='#2c3e50')
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        status_label = tk.Label(
            status_frame,
            textvariable=self.status_var,
            bg='#2c3e50',
            fg='white',
            font=("Arial", 10),
            anchor='w'
        )
        status_label.pack(fill=tk.X, padx=10, pady=5)
    
    def toggle_camera(self):
        """
        Start or stop the camera
        """
        if not self.is_running:
            self.start_camera()
        else:
            self.stop_camera()
    
    def start_camera(self):
        """
        Start the camera and emotion recognition
        """
        if self.recognizer.emotion_cnn.model is None:
            messagebox.showerror("Error", "Model not loaded! Please train the model first.")
            return
        
        self.cap = cv2.VideoCapture(0)
        
        if not self.cap.isOpened():
            messagebox.showerror("Error", "Could not open camera!")
            return
        
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        self.is_running = True
        self.start_button.config(text="Stop Camera", bg='#e74c3c')
        self.status_var.set("Camera running...")
        
        # Start camera thread
        self.camera_thread = threading.Thread(target=self.camera_loop)
        self.camera_thread.daemon = True
        self.camera_thread.start()
    
    def stop_camera(self):
        """
        Stop the camera
        """
        self.is_running = False
        
        if self.cap:
            self.cap.release()
            self.cap = None
        
        self.start_button.config(text="Start Camera", bg='#27ae60')
        self.status_var.set("Camera stopped")
        
        # Clear video display
        self.video_label.config(image='', text="Camera Feed Will Appear Here")
    
    def camera_loop(self):
        """
        Main camera processing loop (runs in separate thread)
        """
        while self.is_running and self.cap:
            ret, frame = self.cap.read()
            
            if not ret:
                break
            
            # Process frame for emotion recognition
            processed_frame = self.recognizer.process_frame(frame)
            self.current_frame = processed_frame
    
    def update_gui(self):
        """
        Update GUI elements (runs in main thread)
        """
        if self.current_frame is not None:
            # Convert frame to display format
            frame_rgb = cv2.cvtColor(self.current_frame, cv2.COLOR_BGR2RGB)
            frame_pil = Image.fromarray(frame_rgb)
            
            # Resize frame to fit display
            display_size = (640, 480)
            frame_pil = frame_pil.resize(display_size, Image.Resampling.LANCZOS)
            
            # Convert to PhotoImage
            frame_tk = ImageTk.PhotoImage(frame_pil)
            
            # Update video label
            self.video_label.config(image=frame_tk, text='')
            self.video_label.image = frame_tk  # Keep a reference
            
            # Update FPS
            if hasattr(self.recognizer, 'fps_counter') and self.recognizer.fps_counter:
                fps = np.mean(self.recognizer.fps_counter)
                self.fps_var.set(f"FPS: {fps:.1f}")
        
        # Schedule next update
        self.root.after(30, self.update_gui)  # Update every 30ms
    
    def process_video_file(self):
        """
        Process a video file
        """
        if self.recognizer.emotion_cnn.model is None:
            messagebox.showerror("Error", "Model not loaded! Please train the model first.")
            return
        
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[("Video files", "*.mp4 *.avi *.mov *.mkv"), ("All files", "*.*")]
        )
        
        if file_path:
            output_path = filedialog.asksaveasfilename(
                title="Save Processed Video As",
                defaultextension=".mp4",
                filetypes=[("MP4 files", "*.mp4"), ("All files", "*.*")]
            )
            
            if output_path:
                self.status_var.set("Processing video...")
                # Process in separate thread to avoid blocking GUI
                thread = threading.Thread(
                    target=self.recognizer.process_video_file,
                    args=(file_path, output_path)
                )
                thread.daemon = True
                thread.start()
    
    def train_model(self):
        """
        Train the emotion recognition model
        """
        response = messagebox.askyesno(
            "Train Model",
            "This will train the emotion recognition model. This may take a while. Continue?"
        )
        
        if response:
            self.status_var.set("Training model...")
            messagebox.showinfo(
                "Training Started",
                "Model training started. Check the console for progress. This window will remain responsive."
            )
            
            # Run training in separate thread
            thread = threading.Thread(target=self.run_training)
            thread.daemon = True
            thread.start()
    
    def run_training(self):
        """
        Run model training (in separate thread)
        """
        try:
            os.system("python models/train_model.py")
            self.status_var.set("Training completed!")
            
            # Reload model
            self.recognizer.load_model()
            
        except Exception as e:
            self.status_var.set(f"Training failed: {str(e)}")
    
    def on_closing(self):
        """
        Handle window closing
        """
        if self.is_running:
            self.stop_camera()
        
        self.root.destroy()

def main():
    """
    Main function to run the GUI application
    """
    root = tk.Tk()
    app = EmotionRecognitionGUI(root)
    
    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    
    # Start the GUI
    root.mainloop()

if __name__ == "__main__":
    main()
