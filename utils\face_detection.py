"""
Face detection utilities using OpenCV
"""

import cv2
import numpy as np

class FaceDetector:
    def __init__(self):
        """
        Initialize the face detector with Haar cascades
        """
        # Load pre-trained Haar cascade for face detection
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # Alternative: Load DNN face detector (more accurate but slower)
        self.use_dnn = False
        self.net = None
        self.load_dnn_detector()
    
    def load_dnn_detector(self):
        """
        Load DNN-based face detector (optional, more accurate)
        """
        try:
            # You can download these files from OpenCV's GitHub repository
            # For now, we'll use Haar cascades as the primary method
            pass
        except Exception as e:
            print(f"DNN detector not available: {e}")
            self.use_dnn = False
    
    def detect_faces_haar(self, image, scale_factor=1.1, min_neighbors=5, min_size=(30, 30)):
        """
        Detect faces using Haar cascade classifier
        
        Args:
            image: Input image
            scale_factor: How much the image size is reduced at each scale
            min_neighbors: How many neighbors each candidate rectangle should have to retain it
            min_size: Minimum possible face size
            
        Returns:
            List of face bounding boxes (x, y, w, h)
        """
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # Detect faces
        faces = self.face_cascade.detectMultiScale(
            gray,
            scaleFactor=scale_factor,
            minNeighbors=min_neighbors,
            minSize=min_size,
            flags=cv2.CASCADE_SCALE_IMAGE
        )
        
        return faces
    
    def detect_faces(self, image):
        """
        Main face detection method
        
        Args:
            image: Input image
            
        Returns:
            List of face bounding boxes and extracted face images
        """
        faces = self.detect_faces_haar(image)
        
        face_images = []
        face_coords = []
        
        for (x, y, w, h) in faces:
            # Extract face region with some padding
            padding = int(0.1 * min(w, h))
            x1 = max(0, x - padding)
            y1 = max(0, y - padding)
            x2 = min(image.shape[1], x + w + padding)
            y2 = min(image.shape[0], y + h + padding)
            
            face_img = image[y1:y2, x1:x2]
            
            if face_img.size > 0:
                face_images.append(face_img)
                face_coords.append((x, y, w, h))
        
        return face_coords, face_images
    
    def draw_face_boxes(self, image, faces, emotions=None, confidences=None):
        """
        Draw bounding boxes around detected faces with emotion labels
        
        Args:
            image: Input image
            faces: List of face coordinates (x, y, w, h)
            emotions: List of predicted emotions (optional)
            confidences: List of confidence scores (optional)
            
        Returns:
            Image with drawn bounding boxes and labels
        """
        result_image = image.copy()
        
        for i, (x, y, w, h) in enumerate(faces):
            # Draw rectangle around face
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
            
            # Add emotion label if available
            if emotions is not None and i < len(emotions):
                emotion = emotions[i]
                confidence = confidences[i] if confidences is not None else 0.0
                
                # Prepare label text
                label = f"{emotion}: {confidence:.2f}"
                
                # Calculate text size and position
                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 0.6
                thickness = 2
                (text_width, text_height), _ = cv2.getTextSize(label, font, font_scale, thickness)
                
                # Draw background rectangle for text
                cv2.rectangle(result_image, 
                            (x, y - text_height - 10), 
                            (x + text_width, y), 
                            (0, 255, 0), -1)
                
                # Draw text
                cv2.putText(result_image, label, (x, y - 5), 
                          font, font_scale, (0, 0, 0), thickness)
        
        return result_image
    
    def get_largest_face(self, faces, face_images):
        """
        Get the largest detected face (assuming it's the main subject)
        
        Args:
            faces: List of face coordinates
            face_images: List of face images
            
        Returns:
            Largest face coordinates and image
        """
        if not faces:
            return None, None
        
        # Find the largest face by area
        areas = [w * h for (x, y, w, h) in faces]
        largest_idx = np.argmax(areas)
        
        return faces[largest_idx], face_images[largest_idx]
    
    def is_face_centered(self, face_coords, image_shape, threshold=0.3):
        """
        Check if the detected face is reasonably centered in the image
        
        Args:
            face_coords: Face coordinates (x, y, w, h)
            image_shape: Shape of the image (height, width)
            threshold: Threshold for considering face as centered
            
        Returns:
            Boolean indicating if face is centered
        """
        if face_coords is None:
            return False
        
        x, y, w, h = face_coords
        img_h, img_w = image_shape[:2]
        
        # Calculate face center
        face_center_x = x + w // 2
        face_center_y = y + h // 2
        
        # Calculate image center
        img_center_x = img_w // 2
        img_center_y = img_h // 2
        
        # Calculate relative distance from center
        rel_dist_x = abs(face_center_x - img_center_x) / img_w
        rel_dist_y = abs(face_center_y - img_center_y) / img_h
        
        return rel_dist_x < threshold and rel_dist_y < threshold

if __name__ == "__main__":
    # Test face detection
    detector = FaceDetector()
    
    # Create a sample image (you can replace this with actual image loading)
    sample_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    faces, face_images = detector.detect_faces(sample_image)
    print(f"Detected {len(faces)} faces")
    
    if faces:
        largest_face, largest_face_img = detector.get_largest_face(faces, face_images)
        print(f"Largest face coordinates: {largest_face}")
    
    print("Face detection test completed!")
