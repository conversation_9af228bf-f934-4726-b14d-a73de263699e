"""
Demo script for Real-time Facial Emotion Recognition System
This script provides a quick demonstration of the system capabilities
"""

import os
import sys
import time
import numpy as np

# Add current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """Print demo banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        Real-time Facial Emotion Recognition Demo            ║
    ║                                                              ║
    ║        🎭 Detect emotions in real-time                      ║
    ║        🤖 Powered by Deep Learning                          ║
    ║        📹 Works with webcam or video files                  ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_system_ready():
    """Check if the system is ready for demo"""
    print("🔍 Checking system readiness...")
    
    issues = []
    
    # Check if model exists
    model_path = "models/saved_models/emotion_model.h5"
    if not os.path.exists(model_path):
        issues.append("❌ Trained model not found")
        print("   → Run: python main.py --train")
    else:
        print("✅ Trained model found")
    
    # Check dependencies
    try:
        import tensorflow as tf
        import cv2
        import numpy as np
        from PIL import Image
        print("✅ All dependencies installed")
    except ImportError as e:
        issues.append(f"❌ Missing dependency: {e}")
        print("   → Run: pip install -r requirements.txt")
    
    # Check camera
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            print("✅ Camera available")
            cap.release()
        else:
            print("⚠️  Camera not available (video processing still works)")
    except:
        print("⚠️  Cannot check camera availability")
    
    return len(issues) == 0, issues

def demo_model_architecture():
    """Demonstrate model architecture"""
    print("\n🧠 Model Architecture Demo")
    print("-" * 40)
    
    try:
        from models.emotion_model import EmotionCNN
        
        # Create model
        emotion_cnn = EmotionCNN()
        emotion_cnn.build_model()
        
        print("📊 Model Summary:")
        print(f"   Input Shape: {emotion_cnn.input_shape}")
        print(f"   Output Classes: {emotion_cnn.num_classes}")
        print(f"   Emotion Labels: {', '.join(emotion_cnn.emotion_labels)}")
        
        # Count parameters
        total_params = emotion_cnn.model.count_params()
        print(f"   Total Parameters: {total_params:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating model: {e}")
        return False

def demo_face_detection():
    """Demonstrate face detection capabilities"""
    print("\n👤 Face Detection Demo")
    print("-" * 40)
    
    try:
        from utils.face_detection import FaceDetector
        import cv2
        
        detector = FaceDetector()
        
        # Create sample image with random data (in real scenario, this would be a real image)
        sample_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Detect faces
        faces, face_images = detector.detect_faces(sample_image)
        
        print(f"✅ Face detector initialized")
        print(f"   Cascade loaded: {detector.face_cascade is not None}")
        print(f"   Sample detection result: {len(faces)} faces found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in face detection: {e}")
        return False

def demo_preprocessing():
    """Demonstrate data preprocessing"""
    print("\n🔄 Data Preprocessing Demo")
    print("-" * 40)
    
    try:
        from utils.data_preprocessing import DataPreprocessor
        
        preprocessor = DataPreprocessor()
        
        # Create sample image
        sample_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        # Preprocess
        processed = preprocessor.preprocess_image(sample_image)
        
        print(f"✅ Preprocessing successful")
        print(f"   Original shape: {sample_image.shape}")
        print(f"   Processed shape: {processed.shape}")
        print(f"   Value range: [{processed.min():.3f}, {processed.max():.3f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in preprocessing: {e}")
        return False

def demo_emotion_prediction():
    """Demonstrate emotion prediction"""
    print("\n🎭 Emotion Prediction Demo")
    print("-" * 40)
    
    try:
        from utils.emotion_utils import RealTimeEmotionRecognizer
        
        recognizer = RealTimeEmotionRecognizer()
        
        if recognizer.emotion_cnn.model is None:
            print("❌ Model not loaded - please train first")
            return False
        
        # Create sample preprocessed face
        sample_face = np.random.rand(48, 48, 1).astype('float32')
        
        # Predict emotion
        emotion, confidence, all_predictions = recognizer.emotion_cnn.predict_emotion(sample_face)
        
        print(f"✅ Emotion prediction successful")
        print(f"   Predicted emotion: {emotion}")
        print(f"   Confidence: {confidence:.3f}")
        print(f"   All predictions: {[f'{p:.3f}' for p in all_predictions]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in emotion prediction: {e}")
        return False

def interactive_demo_menu():
    """Show interactive demo menu"""
    print("\n🎮 Interactive Demo Options")
    print("-" * 40)
    print("1. 🖥️  Launch GUI Application")
    print("2. 💻 Launch Console Application")
    print("3. 🎬 Process Video File")
    print("4. 🏋️  Train Model")
    print("5. 🧪 Run System Tests")
    print("6. ❌ Exit Demo")
    
    while True:
        try:
            choice = input("\nSelect option (1-6): ").strip()
            
            if choice == '1':
                print("🚀 Launching GUI application...")
                os.system("python main.py")
                break
            elif choice == '2':
                print("🚀 Launching console application...")
                os.system("python main.py --mode console")
                break
            elif choice == '3':
                video_path = input("Enter video file path: ").strip()
                if video_path:
                    print(f"🚀 Processing video: {video_path}")
                    os.system(f'python main.py --mode video --video "{video_path}"')
                break
            elif choice == '4':
                print("🚀 Starting model training...")
                os.system("python main.py --train")
                break
            elif choice == '5':
                print("🚀 Running system tests...")
                os.system("python test_system.py")
                break
            elif choice == '6':
                print("👋 Exiting demo...")
                break
            else:
                print("❌ Invalid choice. Please select 1-6.")
                
        except KeyboardInterrupt:
            print("\n👋 Demo interrupted by user")
            break

def main():
    """Main demo function"""
    print_banner()
    
    # Check system readiness
    ready, issues = check_system_ready()
    
    if not ready:
        print("\n❌ System not ready for demo:")
        for issue in issues:
            print(f"   {issue}")
        print("\n🔧 Please fix the issues above and run the demo again.")
        return
    
    print("\n✅ System ready for demo!")
    
    # Run component demos
    demos = [
        ("Model Architecture", demo_model_architecture),
        ("Face Detection", demo_face_detection),
        ("Data Preprocessing", demo_preprocessing),
        ("Emotion Prediction", demo_emotion_prediction)
    ]
    
    print("\n🔬 Running Component Demos...")
    print("=" * 50)
    
    all_passed = True
    for name, demo_func in demos:
        print(f"\n{name}:")
        success = demo_func()
        if not success:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ All component demos passed!")
        
        # Show interactive menu
        interactive_demo_menu()
    else:
        print("❌ Some component demos failed. Please check the errors above.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
