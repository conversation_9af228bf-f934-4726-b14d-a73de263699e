"""
Main entry point for Real-time Facial Emotion Recognition System
"""

import sys
import os
import argparse
import tkinter as tk

# Add current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.interface import Emotion<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.emotion_utils import RealTimeEmotionRecognizer
from models.train_model import EmotionModelTrainer

def run_gui():
    """
    Run the GUI application
    """
    print("Starting Emotion Recognition GUI...")
    root = tk.Tk()
    app = EmotionRecognitionGUI(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()

def run_console():
    """
    Run the console-based real-time recognition
    """
    print("Starting console-based emotion recognition...")
    recognizer = RealTimeEmotionRecognizer()
    
    if recognizer.emotion_cnn.model is not None:
        recognizer.start_webcam_recognition()
    else:
        print("Error: Model not found!")
        print("Please train the model first using: python main.py --train")

def train_model():
    """
    Train the emotion recognition model
    """
    print("Starting model training...")
    trainer = EmotionModelTrainer()
    
    # Check if FER2013 dataset is available
    fer2013_path = "data/fer2013.csv"
    
    if os.path.exists(fer2013_path):
        print("FER2013 dataset found. Loading data...")
        (X_train, y_train), (X_val, y_val) = trainer.preprocessor.load_fer2013_data(fer2013_path)
        
        if X_train is not None:
            print(f"Loaded {len(X_train)} training samples and {len(X_val)} validation samples")
            trainer.train_model(X_train, y_train, X_val, y_val, epochs=50)
        else:
            print("Failed to load FER2013 data. Using sample data instead.")
            trainer.train_with_sample_data()
    else:
        print("FER2013 dataset not found. Training with sample data for demonstration.")
        print("To use real data, download FER2013 dataset and place fer2013.csv in the data/ folder.")
        trainer.train_with_sample_data()
    
    print("Training completed!")

def process_video(video_path, output_path=None):
    """
    Process a video file for emotion recognition
    
    Args:
        video_path: Path to input video
        output_path: Path to save output video
    """
    print(f"Processing video: {video_path}")
    recognizer = RealTimeEmotionRecognizer()
    
    if recognizer.emotion_cnn.model is not None:
        recognizer.process_video_file(video_path, output_path)
    else:
        print("Error: Model not found!")
        print("Please train the model first using: python main.py --train")

def check_dependencies():
    """
    Check if all required dependencies are installed
    """
    required_packages = [
        'tensorflow', 'opencv-python', 'numpy', 'matplotlib', 
        'seaborn', 'scikit-learn', 'Pillow', 'pandas'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nPlease install missing packages using:")
        print("pip install -r requirements.txt")
        return False
    
    print("All required dependencies are installed!")
    return True

def print_system_info():
    """
    Print system information and model status
    """
    print("=" * 50)
    print("Real-time Facial Emotion Recognition System")
    print("=" * 50)
    
    # Check model status
    model_path = "models/saved_models/emotion_model.h5"
    model_exists = os.path.exists(model_path)
    
    print(f"Model Status: {'✓ Loaded' if model_exists else '✗ Not Found'}")
    
    if not model_exists:
        print("Note: Train the model first using --train option")
    
    # Check camera availability
    import cv2
    cap = cv2.VideoCapture(0)
    camera_available = cap.isOpened()
    cap.release()
    
    print(f"Camera Status: {'✓ Available' if camera_available else '✗ Not Available'}")
    
    print("=" * 50)

def main():
    """
    Main function with command line argument parsing
    """
    parser = argparse.ArgumentParser(
        description="Real-time Facial Emotion Recognition using Deep Learning"
    )
    
    parser.add_argument(
        '--mode', 
        choices=['gui', 'console', 'train', 'video'],
        default='gui',
        help='Run mode: gui (default), console, train, or video'
    )
    
    parser.add_argument(
        '--train',
        action='store_true',
        help='Train the emotion recognition model'
    )
    
    parser.add_argument(
        '--video',
        type=str,
        help='Path to video file to process'
    )
    
    parser.add_argument(
        '--output',
        type=str,
        help='Output path for processed video'
    )
    
    parser.add_argument(
        '--check-deps',
        action='store_true',
        help='Check if all dependencies are installed'
    )
    
    parser.add_argument(
        '--info',
        action='store_true',
        help='Show system information'
    )
    
    args = parser.parse_args()
    
    # Show system info
    if args.info:
        print_system_info()
        return
    
    # Check dependencies
    if args.check_deps:
        check_dependencies()
        return
    
    # Check dependencies before running
    if not check_dependencies():
        return
    
    # Handle different modes
    if args.train or args.mode == 'train':
        train_model()
    elif args.video or args.mode == 'video':
        video_path = args.video
        if not video_path:
            video_path = input("Enter video file path: ")
        
        output_path = args.output
        if not output_path:
            output_path = input("Enter output file path (or press Enter to skip): ")
            if not output_path:
                output_path = None
        
        process_video(video_path, output_path)
    elif args.mode == 'console':
        run_console()
    else:  # Default to GUI
        run_gui()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        print(f"An error occurred: {e}")
        import traceback
        traceback.print_exc()
