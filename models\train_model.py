"""
Training script for the emotion recognition model
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from tensorflow.keras.callbacks import Model<PERSON>heckpoint, EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import sys

# Add parent directory to path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.emotion_model import EmotionCNN
from utils.data_preprocessing import DataPreprocessor, create_sample_data

class EmotionModelTrainer:
    def __init__(self, model_save_path="models/saved_models/emotion_model.h5"):
        """
        Initialize the model trainer
        
        Args:
            model_save_path: Path to save the trained model
        """
        self.model_save_path = model_save_path
        self.emotion_cnn = EmotionCNN()
        self.preprocessor = DataPreprocessor()
        self.history = None
    
    def setup_callbacks(self):
        """
        Setup training callbacks
        """
        callbacks = [
            ModelCheckpoint(
                self.model_save_path,
                monitor='val_accuracy',
                save_best_only=True,
                mode='max',
                verbose=1
            ),
            EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            )
        ]
        return callbacks
    
    def create_data_generator(self):
        """
        Create data generator for augmentation during training
        """
        datagen = ImageDataGenerator(
            rotation_range=10,
            width_shift_range=0.1,
            height_shift_range=0.1,
            horizontal_flip=True,
            zoom_range=0.1,
            fill_mode='nearest'
        )
        return datagen
    
    def train_model(self, X_train, y_train, X_val, y_val, epochs=50, batch_size=32):
        """
        Train the emotion recognition model
        
        Args:
            X_train: Training images
            y_train: Training labels
            X_val: Validation images
            y_val: Validation labels
            epochs: Number of training epochs
            batch_size: Batch size for training
        """
        print("Building and compiling model...")
        self.emotion_cnn.build_model()
        self.emotion_cnn.compile_model()
        
        print("Model architecture:")
        self.emotion_cnn.get_model_summary()
        
        # Setup callbacks
        callbacks = self.setup_callbacks()
        
        # Create data generator
        datagen = self.create_data_generator()
        datagen.fit(X_train)
        
        print(f"Starting training with {len(X_train)} training samples and {len(X_val)} validation samples...")
        
        # Train the model
        self.history = self.emotion_cnn.model.fit(
            datagen.flow(X_train, y_train, batch_size=batch_size),
            steps_per_epoch=len(X_train) // batch_size,
            epochs=epochs,
            validation_data=(X_val, y_val),
            callbacks=callbacks,
            verbose=1
        )
        
        print("Training completed!")
        return self.history
    
    def evaluate_model(self, X_test, y_test):
        """
        Evaluate the trained model
        
        Args:
            X_test: Test images
            y_test: Test labels
        """
        if self.emotion_cnn.model is None:
            print("No model to evaluate. Please train the model first.")
            return
        
        print("Evaluating model...")
        test_loss, test_accuracy = self.emotion_cnn.model.evaluate(X_test, y_test, verbose=0)
        
        print(f"Test Loss: {test_loss:.4f}")
        print(f"Test Accuracy: {test_accuracy:.4f}")
        
        return test_loss, test_accuracy
    
    def plot_training_history(self, save_path="training_history.png"):
        """
        Plot training history
        """
        if self.history is None:
            print("No training history available.")
            return
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # Plot accuracy
        ax1.plot(self.history.history['accuracy'], label='Training Accuracy')
        ax1.plot(self.history.history['val_accuracy'], label='Validation Accuracy')
        ax1.set_title('Model Accuracy')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.legend()
        ax1.grid(True)
        
        # Plot loss
        ax2.plot(self.history.history['loss'], label='Training Loss')
        ax2.plot(self.history.history['val_loss'], label='Validation Loss')
        ax2.set_title('Model Loss')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(save_path)
        plt.show()
        
        print(f"Training history plot saved to {save_path}")
    
    def train_with_sample_data(self, epochs=20):
        """
        Train the model with sample data (for demonstration when real dataset is not available)
        """
        print("Creating sample training data...")
        X_train, y_train = create_sample_data()
        X_val, y_val = create_sample_data()
        
        # Reduce sample size for faster training
        X_train = X_train[:500]
        y_train = y_train[:500]
        X_val = X_val[:100]
        y_val = y_val[:100]
        
        print(f"Training with sample data: {X_train.shape[0]} training samples")
        
        # Train the model
        self.train_model(X_train, y_train, X_val, y_val, epochs=epochs, batch_size=16)
        
        # Evaluate
        self.evaluate_model(X_val, y_val)
        
        # Plot history
        self.plot_training_history()

def main():
    """
    Main training function
    """
    print("Emotion Recognition Model Training")
    print("=" * 40)
    
    # Create trainer
    trainer = EmotionModelTrainer()
    
    # Check if FER2013 dataset is available
    fer2013_path = "data/fer2013.csv"
    
    if os.path.exists(fer2013_path):
        print("FER2013 dataset found. Loading data...")
        (X_train, y_train), (X_val, y_val) = trainer.preprocessor.load_fer2013_data(fer2013_path)
        
        if X_train is not None:
            print(f"Loaded {len(X_train)} training samples and {len(X_val)} validation samples")
            trainer.train_model(X_train, y_train, X_val, y_val, epochs=50)
        else:
            print("Failed to load FER2013 data. Using sample data instead.")
            trainer.train_with_sample_data()
    else:
        print("FER2013 dataset not found. Training with sample data for demonstration.")
        print("To use real data, download FER2013 dataset and place fer2013.csv in the data/ folder.")
        trainer.train_with_sample_data()
    
    print("\nTraining completed! Model saved to:", trainer.model_save_path)

if __name__ == "__main__":
    main()
