"""
Setup script for Real-time Facial Emotion Recognition System
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("Error: Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✓ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ All packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing packages: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        "models/saved_models",
        "data",
        "logs",
        "output"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✓ Created directory: {directory}")
        else:
            print(f"✓ Directory exists: {directory}")

def download_sample_data():
    """Download sample data or create instructions"""
    data_dir = "data"
    readme_path = os.path.join(data_dir, "README.md")
    
    if not os.path.exists(readme_path):
        readme_content = """# Data Directory

This directory is for storing training data for the emotion recognition model.

## FER2013 Dataset

To use the FER2013 dataset:

1. Download the FER2013 dataset from Kaggle:
   https://www.kaggle.com/datasets/msambare/fer2013

2. Extract the `fer2013.csv` file to this directory

3. The training script will automatically detect and use this dataset

## Alternative Datasets

You can also use other emotion recognition datasets by modifying the data loading code in `utils/data_preprocessing.py`.

## Sample Data

If no dataset is available, the system will generate sample data for demonstration purposes.
"""
        
        with open(readme_path, 'w') as f:
            f.write(readme_content)
        
        print(f"✓ Created data directory instructions: {readme_path}")

def test_camera():
    """Test camera availability"""
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        
        if cap.isOpened():
            print("✓ Camera is available")
            cap.release()
            return True
        else:
            print("⚠ Camera not available - video processing will still work")
            return False
    except ImportError:
        print("⚠ OpenCV not installed - cannot test camera")
        return False

def create_launcher_scripts():
    """Create launcher scripts for different platforms"""
    
    # Windows batch file
    if platform.system() == "Windows":
        batch_content = """@echo off
echo Starting Real-time Facial Emotion Recognition System
python main.py
pause
"""
        with open("run_emotion_recognition.bat", 'w') as f:
            f.write(batch_content)
        print("✓ Created Windows launcher: run_emotion_recognition.bat")
    
    # Unix shell script
    else:
        shell_content = """#!/bin/bash
echo "Starting Real-time Facial Emotion Recognition System"
python3 main.py
"""
        with open("run_emotion_recognition.sh", 'w') as f:
            f.write(shell_content)
        
        # Make executable
        os.chmod("run_emotion_recognition.sh", 0o755)
        print("✓ Created Unix launcher: run_emotion_recognition.sh")

def run_initial_test():
    """Run initial system test"""
    print("\nRunning initial system test...")
    
    try:
        # Import main modules to check for errors
        from models.emotion_model import EmotionCNN
        from utils.data_preprocessing import DataPreprocessor
        from utils.face_detection import FaceDetector
        
        print("✓ All modules imported successfully")
        
        # Test model creation
        emotion_cnn = EmotionCNN()
        emotion_cnn.build_model()
        print("✓ Model architecture created successfully")
        
        # Test preprocessing
        preprocessor = DataPreprocessor()
        print("✓ Data preprocessor initialized successfully")
        
        # Test face detection
        face_detector = FaceDetector()
        print("✓ Face detector initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Initial test failed: {e}")
        return False

def print_setup_summary():
    """Print setup summary and next steps"""
    print("\n" + "="*60)
    print("SETUP COMPLETE!")
    print("="*60)
    
    print("\nNext Steps:")
    print("1. Train the model:")
    print("   python main.py --train")
    print("   (This will use sample data if FER2013 dataset is not available)")
    
    print("\n2. Run the application:")
    print("   python main.py                    # GUI mode (recommended)")
    print("   python main.py --mode console     # Console mode")
    
    print("\n3. Process video files:")
    print("   python main.py --mode video --video path/to/video.mp4")
    
    print("\n4. Run tests:")
    print("   python test_system.py")
    
    print("\nFor more information, see README.md")
    
    # Platform-specific instructions
    if platform.system() == "Windows":
        print("\nWindows users can also double-click: run_emotion_recognition.bat")
    else:
        print("\nUnix users can also run: ./run_emotion_recognition.sh")

def main():
    """Main setup function"""
    print("Real-time Facial Emotion Recognition System Setup")
    print("="*60)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install requirements
    if not install_requirements():
        return False
    
    # Create directories
    create_directories()
    
    # Create data instructions
    download_sample_data()
    
    # Test camera
    test_camera()
    
    # Create launcher scripts
    create_launcher_scripts()
    
    # Run initial test
    if not run_initial_test():
        print("\n⚠ Setup completed with warnings. Some components may not work correctly.")
        return False
    
    # Print summary
    print_setup_summary()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ Setup completed successfully!")
        else:
            print("\n✗ Setup completed with errors.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\nSetup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nSetup failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
