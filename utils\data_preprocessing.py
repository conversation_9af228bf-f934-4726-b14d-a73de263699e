"""
Data preprocessing utilities for facial emotion recognition
"""

import cv2
import numpy as np
from tensorflow.keras.utils import to_categorical
import os

class DataPreprocessor:
    def __init__(self, target_size=(48, 48)):
        """
        Initialize the data preprocessor
        
        Args:
            target_size: Target size for resizing images
        """
        self.target_size = target_size
    
    def preprocess_image(self, image):
        """
        Preprocess a single image for emotion recognition
        
        Args:
            image: Input image (BGR or grayscale)
            
        Returns:
            Preprocessed image ready for model input
        """
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # Resize to target size
        resized = cv2.resize(gray, self.target_size)
        
        # Normalize pixel values to [0, 1]
        normalized = resized.astype('float32') / 255.0
        
        # Add channel dimension
        processed = np.expand_dims(normalized, axis=-1)
        
        return processed
    
    def preprocess_face_for_emotion(self, face_image):
        """
        Preprocess detected face for emotion recognition
        
        Args:
            face_image: Detected face image
            
        Returns:
            Preprocessed face ready for emotion prediction
        """
        # Apply histogram equalization for better contrast
        if len(face_image.shape) == 3:
            gray = cv2.cvtColor(face_image, cv2.COLOR_BGR2GRAY)
        else:
            gray = face_image.copy()
        
        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # Resize to model input size
        resized = cv2.resize(enhanced, self.target_size)
        
        # Normalize
        normalized = resized.astype('float32') / 255.0
        
        # Add channel dimension
        processed = np.expand_dims(normalized, axis=-1)
        
        return processed
    
    def augment_image(self, image):
        """
        Apply data augmentation to an image
        
        Args:
            image: Input image
            
        Returns:
            List of augmented images
        """
        augmented_images = []
        
        # Original image
        augmented_images.append(image)
        
        # Horizontal flip
        flipped = cv2.flip(image, 1)
        augmented_images.append(flipped)
        
        # Slight rotation
        rows, cols = image.shape[:2]
        for angle in [-10, 10]:
            M = cv2.getRotationMatrix2D((cols/2, rows/2), angle, 1)
            rotated = cv2.warpAffine(image, M, (cols, rows))
            augmented_images.append(rotated)
        
        # Brightness adjustment
        for beta in [-20, 20]:
            bright = cv2.convertScaleAbs(image, alpha=1.0, beta=beta)
            augmented_images.append(bright)
        
        return augmented_images
    
    def prepare_training_data(self, images, labels):
        """
        Prepare training data with preprocessing and augmentation
        
        Args:
            images: List of training images
            labels: List of corresponding labels
            
        Returns:
            Preprocessed images and one-hot encoded labels
        """
        processed_images = []
        processed_labels = []
        
        for img, label in zip(images, labels):
            # Preprocess image
            processed_img = self.preprocess_image(img)
            processed_images.append(processed_img)
            processed_labels.append(label)
            
            # Apply augmentation
            augmented = self.augment_image(img)
            for aug_img in augmented[1:]:  # Skip original image
                processed_aug = self.preprocess_image(aug_img)
                processed_images.append(processed_aug)
                processed_labels.append(label)
        
        # Convert to numpy arrays
        X = np.array(processed_images)
        y = to_categorical(processed_labels, num_classes=7)
        
        return X, y
    
    def load_fer2013_data(self, csv_path):
        """
        Load and preprocess FER2013 dataset
        
        Args:
            csv_path: Path to FER2013 CSV file
            
        Returns:
            Training and validation data
        """
        import pandas as pd
        
        try:
            # Load CSV file
            df = pd.read_csv(csv_path)
            
            # Extract images and labels
            images = []
            labels = []
            
            for idx, row in df.iterrows():
                # Convert pixel string to image array
                pixels = np.array(row['pixels'].split(), dtype='float32')
                image = pixels.reshape(48, 48)
                
                images.append(image)
                labels.append(row['emotion'])
            
            # Split into train and validation
            train_size = int(0.8 * len(images))
            
            train_images = images[:train_size]
            train_labels = labels[:train_size]
            val_images = images[train_size:]
            val_labels = labels[train_size:]
            
            # Preprocess data
            X_train, y_train = self.prepare_training_data(train_images, train_labels)
            X_val, y_val = self.prepare_training_data(val_images, val_labels)
            
            return (X_train, y_train), (X_val, y_val)
            
        except Exception as e:
            print(f"Error loading FER2013 data: {e}")
            return None, None

def create_sample_data():
    """
    Create sample data for testing when real dataset is not available
    """
    # Generate random sample data
    num_samples = 1000
    X = np.random.rand(num_samples, 48, 48, 1).astype('float32')
    y = to_categorical(np.random.randint(0, 7, num_samples), num_classes=7)
    
    return X, y

if __name__ == "__main__":
    # Test preprocessing
    preprocessor = DataPreprocessor()
    
    # Create sample image
    sample_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    processed = preprocessor.preprocess_image(sample_image)
    
    print(f"Original shape: {sample_image.shape}")
    print(f"Processed shape: {processed.shape}")
    print("Preprocessing test completed successfully!")
